#!/usr/bin/env python3
"""
Тестовый файл для проверки исправления навигации в истории ЕНТ
"""

def test_callback_data_creation():
    """Проверяем создание правильного callback_data для истории"""
    
    print("🧪 Тестирование создания callback_data для истории...")
    
    try:
        # Читаем файл с функциями навигации
        with open("common/tests_statistics/register_handlers.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Проверяем, что создается правильный callback
        callback_creation_patterns = [
            "new_callback = CBQ(",
            "data=f\"ent_history_{group_id}_{student_id}\"",
            "🔍 СОЗДАЕМ CALLBACK ДЛЯ ИСТОРИИ: ent_history_{group_id}_{student_id}",
            "await show_ent_student_history(new_callback, state)"
        ]
        
        for pattern in callback_creation_patterns:
            if pattern in content:
                print(f"✅ Найдено создание callback: {pattern[:50]}...")
            else:
                print(f"❌ НЕ найдено создание callback: {pattern[:50]}...")
                return False
        
        print("✅ Callback для истории создается правильно")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке создания callback: {e}")
        return False

def test_navigation_logic():
    """Проверяем логику навигации"""
    
    print("\n🧪 Тестирование логики навигации...")
    
    try:
        # Проверяем, что переходы настроены правильно
        from curator.states.states_tests import STATE_TRANSITIONS, CuratorTestsStatisticsStates
        
        # Проверяем переход из детального просмотра к списку
        expected_transition = STATE_TRANSITIONS.get(CuratorTestsStatisticsStates.ent_history_detail)
        assert expected_transition == CuratorTestsStatisticsStates.ent_history
        print("✅ Переход ent_history_detail → ent_history настроен правильно")
        
        # Проверяем переход из списка к результату
        expected_transition = STATE_TRANSITIONS.get(CuratorTestsStatisticsStates.ent_history)
        assert expected_transition == CuratorTestsStatisticsStates.ent_result
        print("✅ Переход ent_history → ent_result настроен правильно")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке логики навигации: {e}")
        return False

def test_expected_flow():
    """Проверяем ожидаемый поток навигации"""
    
    print("\n🧪 Тестирование ожидаемого потока навигации...")
    
    navigation_flow = {
        "Детальный просмотр теста": {
            "function": "show_ent_history_detail",
            "state": "ent_history_detail",
            "screen": "📊 Детальные результаты теста"
        },
        "Список тестов": {
            "function": "show_ent_student_history", 
            "state": "ent_history",
            "screen": "📊 История пробных ЕНТ"
        },
        "Результат студента": {
            "function": "show_trial_ent_student_detail",
            "state": "ent_result", 
            "screen": "📊 Результат пробного ЕНТ"
        }
    }
    
    print("📋 Правильный поток навигации:")
    print("   1. Детальный просмотр теста (ent_history_detail)")
    print("      ↓ Нажатие 'Назад'")
    print("   2. Список тестов (ent_history)")
    print("      ↓ Нажатие 'Назад'")
    print("   3. Результат студента (ent_result)")
    
    print("\n🔧 Проблема была:")
    print("   ❌ Из детального просмотра сразу попадали в результат студента")
    print("   ❌ Пропускался экран со списком тестов")
    print("   ❌ Функция show_ent_student_history получала callback_data='back'")
    print("   ❌ Парсинг 'back' как 'ent_history_1_33' вызывал IndexError")
    
    print("\n✅ Теперь исправлено:")
    print("   ✅ Создается правильный callback с данными 'ent_history_1_33'")
    print("   ✅ Функция show_ent_student_history получает правильные данные")
    print("   ✅ Навигация проходит через все правильные экраны")
    print("   ✅ Пользователь видит список тестов перед возвратом к результату")
    
    return True

def test_callback_data_format():
    """Проверяем формат callback_data"""
    
    print("\n🧪 Тестирование формата callback_data...")
    
    # Тестируем парсинг правильного формата
    test_data = "ent_history_1_33"
    parts = test_data.split("_")
    
    assert len(parts) == 4
    assert parts[0] == "ent"
    assert parts[1] == "history"
    assert int(parts[2]) == 1  # group_id
    assert int(parts[3]) == 33  # student_id
    print("✅ Формат 'ent_history_GROUP_ID_STUDENT_ID' парсится правильно")
    
    # Тестируем, что неправильный формат вызывает ошибку
    wrong_data = "back"
    parts = wrong_data.split("_")
    
    try:
        group_id = int(parts[2])  # Должен вызвать IndexError
        assert False, "Должен был быть IndexError"
    except IndexError:
        print("✅ Неправильный формат 'back' правильно вызывает IndexError")
    
    return True

if __name__ == "__main__":
    print("🧪 Тестирование исправления навигации в истории ЕНТ...")
    
    success = True
    
    if not test_callback_data_creation():
        success = False
    
    if not test_navigation_logic():
        success = False
    
    if not test_expected_flow():
        success = False
    
    if not test_callback_data_format():
        success = False
    
    if success:
        print("\n🎉 Все тесты прошли успешно!")
        print("\n📋 Исправленные проблемы:")
        print("   ✅ Функция handle_ent_history_back_to_list создает правильный callback")
        print("   ✅ show_ent_student_history получает данные в формате 'ent_history_1_33'")
        print("   ✅ Навигация проходит через правильные экраны")
        print("   ✅ Пользователь не пропускает экран со списком тестов")
        print("\n🔧 Как это работает:")
        print("   1. Пользователь нажимает 'Назад' из детального просмотра")
        print("   2. Вызывается handle_ent_history_back_to_list")
        print("   3. Создается новый CallbackQuery с data='ent_history_1_33'")
        print("   4. Вызывается show_ent_student_history с правильными данными")
        print("   5. Показывается список тестов студента")
        print("\n🚀 Теперь навигация должна работать правильно!")
    else:
        print("\n❌ Некоторые тесты не прошли. Проверьте исправления.")
